import { MalouErrorCode } from '@malou-io/package-utils';

describe('CreateRestaurantUseCase - Hyperline Integration', () => {
    let mockSubscriptionsProvider: any;
    let mockRestaurantsRepository: any;

    beforeEach(() => {
        // Create simple mocks
        mockSubscriptionsProvider = {
            updateSubscriptionsProviderLocation: jest.fn(),
        };

        mockRestaurantsRepository = {
            updateOne: jest.fn(),
            deleteOne: jest.fn(),
        };
    });

    describe('Hyperline Integration Behavior', () => {
        const mockRestaurant = { _id: 'test-restaurant-id' };
        const subscriptionsProviderId = 'test-hyperline-id';

        it('should delete restaurant and throw error when subscriptionsProvider fails', async () => {
            // Mock subscriptionsProvider to throw an error
            const hyperlineError = new Error('Hyperline API failed');
            mockSubscriptionsProvider.updateSubscriptionsProviderLocation.mockRejectedValue(hyperlineError);

            // Mock successful restaurant deletion
            mockRestaurantsRepository.deleteOne.mockResolvedValue({});

            // Simulate the behavior we expect from CreateRestaurantUseCase
            const simulateCreateRestaurantFailure = async () => {
                try {
                    // This simulates the call to updateSubscriptionsProviderLocation
                    await mockSubscriptionsProvider.updateSubscriptionsProviderLocation({
                        subscriptionsProviderLocationId: subscriptionsProviderId,
                        malouRestaurantId: mockRestaurant._id.toString(),
                    });

                    // If we reach here, the subscriptionsProvider didn't fail
                    await mockRestaurantsRepository.updateOne({
                        filter: { _id: mockRestaurant._id },
                        update: { subscriptionsProviderId },
                    });
                } catch (error) {
                    // If subscriptionsProvider fails, delete the restaurant
                    await mockRestaurantsRepository.deleteOne({
                        filter: { _id: mockRestaurant._id },
                    });

                    // Throw a MalouError-like error
                    const malouError = new Error('Hyperline integration failed');
                    (malouError as any).malouErrorCode = MalouErrorCode.HYPERLINE_INTEGRATION_ERROR;
                    throw malouError;
                }
            };

            await expect(simulateCreateRestaurantFailure()).rejects.toThrow(
                expect.objectContaining({
                    malouErrorCode: MalouErrorCode.HYPERLINE_INTEGRATION_ERROR,
                    message: 'Hyperline integration failed',
                })
            );

            // Verify that updateSubscriptionsProviderLocation was called
            expect(mockSubscriptionsProvider.updateSubscriptionsProviderLocation).toHaveBeenCalledWith({
                subscriptionsProviderLocationId: subscriptionsProviderId,
                malouRestaurantId: mockRestaurant._id.toString(),
            });

            // Verify that restaurant was deleted after failure
            expect(mockRestaurantsRepository.deleteOne).toHaveBeenCalledWith({
                filter: { _id: mockRestaurant._id },
            });

            // Verify that updateOne was NOT called (since the subscriptionsProvider failed first)
            expect(mockRestaurantsRepository.updateOne).not.toHaveBeenCalled();
        });

        it('should set subscriptionsProviderId when subscriptionsProvider succeeds', async () => {
            // Mock successful subscriptionsProvider call
            mockSubscriptionsProvider.updateSubscriptionsProviderLocation.mockResolvedValue(undefined);

            // Mock successful restaurant update
            mockRestaurantsRepository.updateOne.mockResolvedValue({});

            // Simulate the behavior we expect from CreateRestaurantUseCase
            const simulateCreateRestaurantSuccess = async () => {
                try {
                    // This simulates the call to updateSubscriptionsProviderLocation
                    await mockSubscriptionsProvider.updateSubscriptionsProviderLocation({
                        subscriptionsProviderLocationId: subscriptionsProviderId,
                        malouRestaurantId: mockRestaurant._id.toString(),
                    });

                    // If successful, update the restaurant with subscriptionsProviderId
                    await mockRestaurantsRepository.updateOne({
                        filter: { _id: mockRestaurant._id },
                        update: { subscriptionsProviderId },
                    });

                    return { success: true };
                } catch (error) {
                    // If subscriptionsProvider fails, delete the restaurant
                    await mockRestaurantsRepository.deleteOne({
                        filter: { _id: mockRestaurant._id },
                    });

                    // Throw a MalouError-like error
                    const malouError = new Error('Hyperline integration failed');
                    (malouError as any).malouErrorCode = MalouErrorCode.HYPERLINE_INTEGRATION_ERROR;
                    throw malouError;
                }
            };

            const result = await simulateCreateRestaurantSuccess();

            // Verify that updateSubscriptionsProviderLocation was called
            expect(mockSubscriptionsProvider.updateSubscriptionsProviderLocation).toHaveBeenCalledWith({
                subscriptionsProviderLocationId: subscriptionsProviderId,
                malouRestaurantId: mockRestaurant._id.toString(),
            });

            // Verify that restaurant was updated with subscriptionsProviderId
            expect(mockRestaurantsRepository.updateOne).toHaveBeenCalledWith({
                filter: { _id: mockRestaurant._id },
                update: { subscriptionsProviderId },
            });

            // Verify that restaurant was NOT deleted (success scenario)
            expect(mockRestaurantsRepository.deleteOne).not.toHaveBeenCalled();

            // Verify success
            expect(result).toEqual({ success: true });
        });

        it('should not call subscriptionsProvider when subscriptionsProviderId is not provided', async () => {
            // Simulate the behavior when no subscriptionsProviderId is provided
            const simulateCreateRestaurantWithoutSubscriptionsProvider = async () => {
                const noSubscriptionsProviderId = undefined;

                if (noSubscriptionsProviderId) {
                    // This simulates the call to updateSubscriptionsProviderLocation
                    await mockSubscriptionsProvider.updateSubscriptionsProviderLocation({
                        subscriptionsProviderLocationId: noSubscriptionsProviderId,
                        malouRestaurantId: mockRestaurant._id.toString(),
                    });

                    // Update the restaurant with subscriptionsProviderId
                    await mockRestaurantsRepository.updateOne({
                        filter: { _id: mockRestaurant._id },
                        update: { subscriptionsProviderId: noSubscriptionsProviderId },
                    });
                }

                return { success: true };
            };

            const result = await simulateCreateRestaurantWithoutSubscriptionsProvider();

            // Verify that subscriptionsProvider methods were NOT called
            expect(mockSubscriptionsProvider.updateSubscriptionsProviderLocation).not.toHaveBeenCalled();

            // Verify that restaurant repository methods related to subscriptions were NOT called
            expect(mockRestaurantsRepository.updateOne).not.toHaveBeenCalled();
            expect(mockRestaurantsRepository.deleteOne).not.toHaveBeenCalled();

            // Verify success
            expect(result).toEqual({ success: true });
        });
    });
});
